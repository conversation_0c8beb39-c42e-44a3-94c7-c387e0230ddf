/**
 * 天地图相关类型定义
 */

/**
 * 位置信息接口
 */
export interface ILocationInfo {
  /** 经度 */
  longitude: number
  /** 纬度 */
  latitude: number
  /** 详细地址 */
  address: string
  /** 省份 */
  province?: string
  /** 城市 */
  city?: string
  /** 区县 */
  district?: string
  /** 街道 */
  street?: string
  /** 获取时间 */
  timestamp: number
}

/**
 * 天地图逆地理编码响应接口
 */
export interface ITiandituGeocodeResponse {
  status: string
  result: {
    formatted_address: string
    addressComponent: {
      nation: string
      province: string
      city: string
      county: string
      road: string
      poi: string
      poi_distance: string
      address: string
    }
  }
}
