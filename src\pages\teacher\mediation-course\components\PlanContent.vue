<script lang="ts" setup>
// 调停补课方案组件
import { computed, ref, watchEffect } from 'vue'
import { useMediationCourseStore } from '@/store/mediationCourse'
import type { AdjustCourseData } from '@/store/mediationCourse'
import type {
  MediationCourseDetail,
  ScheduleChangeApplyParams,
  MediationCoursePlanItem,
} from '@/types/mediationCourse'
import FileUploader from '@/components/common/FileUploader.vue'
import ActionButton from '@/components/common/ActionButton.vue'
import { submitScheduleChangeApply } from '@/service/mediationCourse'

// 定义props
const props = defineProps<{
  id?: string
  mode?: string
  type?: string
  detailData?: MediationCourseDetail | null
  loading?: boolean
}>()

// 定义emit事件
const emit = defineEmits<{
  'prev-step': []
}>()

// 获取调停课store
const mediationCourseStore = useMediationCourseStore()

// 表单数据
const formData = ref({
  mediationType: '', // 调停补课类型
  reason: '', // 调停课原因
  attachments: [] as Array<{ url: string; name: string }>, // 附件列表
})

// 提交loading状态
const submitLoading = ref(false)

// 计算列表标题
const listTitle = computed(() => {
  if (props.type === 'substitute') {
    return '当前代课方案列表'
  }
  return '当前调停补课方案列表'
})

// 将plan_list中的项目转换为AdjustCourseData格式
const convertPlanItemToAdjustCourseData = (
  planItem: MediationCoursePlanItem,
): AdjustCourseData | null => {
  try {
    console.log(planItem)

    // 根据changeType确定类型
    let type: 'stop' | 'change' | 'make_up'
    if (planItem.changeType === 2) {
      type = 'stop' // 停课
    } else if (planItem.changeType === 3) {
      type = 'make_up' // 补课
    } else if (planItem.changeType === 4) {
      type = 'change' // 调课
    } else {
      console.warn('未知的changeType:', planItem.changeType)
      return null
    }

    // 构造AdjustCourseData对象
    const adjustCourseData: AdjustCourseData = {
      type,
      scheduleId: planItem.oldScheduleId || 0,
      key: planItem.key || '',
      teachingTaskId: planItem.teachingTaskId || 0,
      sourceRemark: planItem.remark || '',
      targetRemark: type === 'stop' ? '' : planItem.remark || '',
      ks: planItem.oldSchedule?.ks || 2,
      targetWeek: planItem.targetWeek || 0,
      targetDate: planItem.targetDate || '',
      targetSection: Array.isArray(planItem.targetSection)
        ? planItem.targetSection.join('-')
        : planItem.targetSection || '',
      targetVenueCode: planItem.targetVenue || '',
      teacherCode: '',
      teachingClassCode: '',
      mergeTeachingTaskId: planItem.mergeTeachingTaskId
        ? Number(planItem.mergeTeachingTaskId)
        : null,
      _X_ROW_KEY: `row_${Date.now()}_${Math.random()}`,
    }

    // 补课特有字段
    if (type === 'make_up') {
      adjustCourseData.jxrwid = planItem.teachingTaskId || 0
    }

    return adjustCourseData
  } catch (error) {
    console.error('转换plan_list项目失败:', error, planItem)
    return null
  }
}

// 监听详情数据变化，设置调停课类型、原因和附件，并构造调课数据到store
watchEffect(() => {
  if (props.detailData) {
    // 设置调停补课类型 ttklx (1-因私 2-因公)
    if (props.detailData.ttklx) {
      formData.value.mediationType = String(props.detailData.ttklx)
      console.log('设置调停补课类型:', props.detailData.ttklx)
    }

    // 设置调停课原因 ttkyy
    if (props.detailData.ttkyy) {
      formData.value.reason = props.detailData.ttkyy
      console.log('设置调停课原因:', props.detailData.ttkyy)
    }

    // 设置附件列表 fjlb (格式：文件路径|文件名,文件路径|文件名)
    if (props.detailData.fjlb) {
      const attachmentList = props.detailData.fjlb
        .split(',')
        .map((item) => {
          const [url, name] = item.split('|')
          return { url: url || '', name: name || '' }
        })
        .filter((item) => item.url && item.name) // 过滤掉无效的附件

      formData.value.attachments = attachmentList
      console.log('设置附件列表:', attachmentList)
    }

    // 处理plan_list，构造调课数据到store中
    if (props.detailData.plan_list && props.detailData.plan_list.length > 0) {
      // 清空现有的调课数据
      mediationCourseStore.clearAdjustCourseData()

      props.detailData.plan_list.forEach((planItem) => {
        const adjustCourseData = convertPlanItemToAdjustCourseData(planItem)
        if (adjustCourseData) {
          // 使用key作为标识存储到store中
          mediationCourseStore.addAdjustCourseData(
            adjustCourseData.scheduleId === 0
              ? adjustCourseData.key
              : adjustCourseData.scheduleId + '',
            adjustCourseData,
          )
        }
      })

      console.log('已将plan_list数据构造到store中:', props.detailData.plan_list.length, '条记录')
    }
  }
})

// 获取所有调停补课方案数据
const planList = computed(() => {
  const adjustCourseDataMap = mediationCourseStore.getAllAdjustCourseData()
  return Object.values(adjustCourseDataMap).map((data, index) => ({
    ...data,
    index: index + 1, // 序号从1开始
  }))
})

// 获取类型显示文本
const getTypeText = (type: string) => {
  if (type === 'stop') return '停课'
  if (type === 'make_up') return '补课'
  return '调课'
}

// 获取类型样式类
const getTypeClass = (type: string) => {
  if (type === 'stop') return 'type-stop'
  if (type === 'make_up') return 'type-makeup'
  return 'type-adjust'
}

// 解析原课程信息
const parseSourceInfo = (data: AdjustCourseData) => {
  // 从store中获取对应的plan_list数据来构造正确的显示格式
  if (props.detailData && props.detailData.plan_list) {
    const planItem = props.detailData.plan_list.find((item) => item.key === data.key)
    if (planItem) {
      if (data.type === 'stop') {
        // 停课格式：Linux操作系统服务器管理(软件技术2411) 2025-06-27 第19周 星期5 7-8节 测试
        const oldSchedule = planItem.oldSchedule
        if (oldSchedule && typeof oldSchedule === 'object') {
          const courseName = oldSchedule.kcmc || planItem.teachingCourseName
          const className = oldSchedule.skbjmc || planItem.teachingClassName
          const date = oldSchedule.skrq
          const week = oldSchedule.zc
          const weekDay = oldSchedule.xqs
          const section = oldSchedule.jcshow || oldSchedule.jc
          const venue = oldSchedule.skcdmc || ''

          return `${courseName}(${className}) ${date} 第${week}周 星期${weekDay} ${section}节${venue ? ' ' + venue : ''}`
        }
      } else if (data.type === 'change') {
        // 调课格式：测试课程(选修教学班13848) 2025-07-02 第20周 星期3 5-6节
        const oldSchedule = planItem.oldSchedule
        if (oldSchedule && typeof oldSchedule === 'object') {
          const courseName = oldSchedule.kcmc || planItem.teachingCourseName
          const className = oldSchedule.skbjmc || planItem.teachingClassName
          const date = oldSchedule.skrq
          const week = oldSchedule.zc
          const weekDay = oldSchedule.xqs
          const section = oldSchedule.jcshow || oldSchedule.jc

          return `${courseName}(${className}) ${date} 第${week}周 星期${weekDay} ${section}节`
        }
      } else if (data.type === 'make_up') {
        // 补课格式：面向对象程序设计(软件技术2411)
        const courseName = planItem.teachingCourseName
        const className = planItem.teachingClassName
        return `${courseName}(${className})`
      }
    }
  }

  // 兜底逻辑：如果找不到对应的plan_list数据，使用原有逻辑
  if (data.type === 'make_up') {
    return data.sourceRemark || '未知课程'
  }

  const match = data.sourceRemark.match(/\[.*?\](.+?)>>>/)
  if (match) {
    return match[1].trim()
  }
  return data.sourceRemark
}

// 解析目标课程信息
const parseTargetInfo = (data: AdjustCourseData) => {
  if (data.type === 'stop') {
    return '停课'
  }

  // 调课情况下显示目标信息，格式：2025-04-27 第10周 星期7 1-2节
  const parts = []

  if (data.targetDate) {
    // 计算星期几
    const date = new Date(data.targetDate)
    const weekDay = date.getDay() === 0 ? 7 : date.getDay() // 将周日(0)转换为7

    // 组合日期信息：日期 + 周次 + 星期
    parts.push(`${data.targetDate} 第${data.targetWeek}周 星期${weekDay}`)
  }

  if (data.targetSection) {
    parts.push(`${data.targetSection}节`)
  }

  if (data.targetVenueName) {
    parts.push(data.targetVenueName)
  } /* else if (data.targetVenueCode) {
    parts.push(data.targetVenueCode)
  } */

  return parts.length > 0 ? parts.join(' ') : '待安排'
}

// 撤销方案
const handleRevoke = (data: AdjustCourseData) => {
  uni.showModal({
    title: '确认撤销',
    content: `确定要撤销这个${getTypeText(data.type)}方案吗？`,
    success: (res) => {
      if (res.confirm) {
        // 从store中移除数据

        const scheduleIdKey = String(data.scheduleId === 0 ? data.key : data.scheduleId)
        mediationCourseStore.removeAdjustCourseData(scheduleIdKey)

        uni.showToast({
          title: '撤销成功',
          icon: 'success',
        })
      }
    },
  })
}

// 处理上一步
const handlePrevStep = () => {
  emit('prev-step')
}

// 处理提交申请
const handleSubmit = async () => {
  // 如果正在提交中，直接返回
  if (submitLoading.value) {
    return
  }

  try {
    // 设置loading状态
    submitLoading.value = true

    // 检查是否有调停课方案
    if (planList.value.length === 0) {
      submitLoading.value = false
      uni.showToast({
        title: '请先添加调停课方案',
        icon: 'none',
      })
      return
    }

    // 检查必填字段
    if (!formData.value.mediationType) {
      submitLoading.value = false
      uni.showToast({
        title: '请选择调停课类型',
        icon: 'none',
      })
      return
    }

    if (!formData.value.reason.trim()) {
      submitLoading.value = false
      uni.showToast({
        title: '请输入调停课原因',
        icon: 'none',
      })
      return
    }

    // 构建提交数据
    const submitData: ScheduleChangeApplyParams = {
      // 调课方案键值数组，从store中获取所有方案的key
      key: planList.value.map((item) => item.key),
      // 调停课类型：1-因私 2-因公
      ttklx: formData.value.mediationType,
      // 调停课原因
      ttkyy: formData.value.reason,
      // 附件列表，格式：文件路径|文件名
      fjlb: formData.value.attachments.map((file) => `${file.url}|${file.name}`).join(','),
      // 开始时间和结束时间，优先使用详情数据，否则从ScheduleContent组件设置的时间范围获取
      begin_time: getBeginTime(),
      end_time: getEndTime(),
      // 申请ID，编辑时使用详情数据的id，新建时为空字符串
      id: props.id || '',
    }

    // 打印提交数据用于调试
    console.log('准备提交的调课申请数据:', submitData)
    console.log('调课方案详情:', planList.value)

    // 提交调课申请
    const result = await submitScheduleChangeApply(submitData)
    console.log('提交结果:', result)

    // 提交成功后的处理
    // 注意：根据接口定义，这里返回的是data部分，需要根据实际API响应格式调整
    uni.showToast({
      title: '提交成功',
      icon: 'success',
    })

    // 可以考虑清空store中的数据或跳转到列表页面
    mediationCourseStore.clearAdjustCourseData()
    uni.navigateBack({
      delta: 1,
    })
  } catch (error) {
    console.error('提交调课申请失败:', error)
    // 显示错误提示
  } finally {
    // 无论成功还是失败，都要取消loading状态
    submitLoading.value = false
  }
}

// 获取开始时间 - 优先使用详情数据，否则从store中获取ScheduleContent设置的时间范围
const getBeginTime = (): string => {
  // 如果有详情数据且包含开始时间，优先使用详情数据
  if (props.detailData && props.detailData.ttkkssj) {
    return props.detailData.ttkkssj.split(' ')[0] // 只取日期部分，格式：2025-05-01
  }

  // 否则从store中获取
  const timeRange = mediationCourseStore.getTimeRange()
  return mediationCourseStore.formatTimeToDate(timeRange.startDate)
}

// 获取结束时间 - 优先使用详情数据，否则从store中获取ScheduleContent设置的时间范围
const getEndTime = (): string => {
  // 如果有详情数据且包含结束时间，优先使用详情数据
  if (props.detailData && props.detailData.ttkjssj) {
    return props.detailData.ttkjssj.split(' ')[0] // 只取日期部分，格式：2025-07-30
  }

  // 否则从store中获取
  const timeRange = mediationCourseStore.getTimeRange()
  return mediationCourseStore.formatTimeToDate(timeRange.endDate)
}

// 预览附件
const previewAttachment = (file: { url: string; name: string }) => {
  // #ifdef H5
  window.open(file.url, '_blank')
  // #endif

  // #ifdef APP-PLUS
  uni.downloadFile({
    url: file.url,
    success: (res) => {
      if (res.statusCode === 200) {
        uni.openDocument({
          filePath: res.tempFilePath,
          success: () => {
            console.log('打开文档成功')
          },
          fail: (err) => {
            console.error('打开文档失败:', err)
            uni.showToast({
              title: '无法打开该文件',
              icon: 'none',
            })
          },
        })
      }
    },
    fail: (err) => {
      console.error('下载文件失败:', err)
      uni.showToast({
        title: '下载失败',
        icon: 'none',
      })
    },
  })
  // #endif

  // #ifdef MP
  uni.showToast({
    title: '小程序暂不支持该类型文件预览',
    icon: 'none',
  })
  // #endif
}

// 根据文件名获取对应的图标
const getFileIcon = (fileName: string) => {
  const extension = fileName.split('.').pop()?.toLowerCase() || ''

  switch (extension) {
    case 'pdf':
      return 'file-pdf'
    case 'doc':
    case 'docx':
      return 'file-word'
    case 'xls':
    case 'xlsx':
      return 'file-excel'
    case 'ppt':
    case 'pptx':
      return 'file-powerpoint'
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
      return 'file-image'
    default:
      return 'file-icon'
  }
}

// 处理补课按钮点击
const handleMakeupCourse = () => {
  // 清空getCurrentCourseInfo中的数据
  mediationCourseStore.clearCurrentCourseInfo()

  // 跳转到调课页面，添加来源标识
  uni.navigateTo({
    url: '/pages/teacher/mediation-course/adjust-course/index?source=makeup',
  })
}
</script>

<template>
  <view class="content-card">
    <view class="card-content">
      <!-- 调停补课信息表单 -->
      <view class="form-section mb-6">
        <view class="form-title mb-4">调停补课信息</view>

        <!-- 调停补课类型 -->
        <view class="form-item mb-4">
          <view class="form-label mb-2">调停补课类型</view>
          <view class="radio-container">
            <wd-radio-group
              v-model="formData.mediationType"
              :disabled="props.mode === 'view'"
              inline
              shape="dot"
            >
              <wd-radio value="1" class="radio-item">因私</wd-radio>
              <wd-radio value="2" class="radio-item">因公</wd-radio>
            </wd-radio-group>
          </view>
        </view>

        <!-- 调停课原因 -->
        <view class="form-item mb-4">
          <view class="form-label mb-2">调停课原因</view>
          <wd-textarea
            auto-height
            v-model="formData.reason"
            placeholder="请输入调停课原因"
            :maxlength="200"
            show-word-limit
            class="reason-textarea p-2"
            :disabled="props.mode === 'view'"
          />
        </view>

        <!-- 附件上传 - 查看模式下隐藏上传功能 -->
        <view class="form-item mb-4">
          <FileUploader
            v-if="props.mode !== 'view'"
            v-model="formData.attachments"
            title="附件上传"
            upload-type="mediation-course"
            tip-text="支持jpg、png、pdf、word、excel、ppt等常见文件格式"
          />
          <!-- 查看模式下只显示附件列表 -->
          <view v-else>
            <view class="text-sm font-medium mb-2">附件列表</view>
            <view v-if="formData.attachments.length > 0" class="attachment-list">
              <view
                v-for="(file, index) in formData.attachments"
                :key="index"
                class="attachment-item"
              >
                <view class="attachment-info" @click="previewAttachment(file)">
                  <wd-icon :name="getFileIcon(file.name)" size="18px" class="mr-2" />
                  <text class="attachment-name">{{ file.name }}</text>
                </view>
                <view class="attachment-actions">
                  <view class="attachment-action" @click="previewAttachment(file)">
                    <wd-icon name="view" size="18px" color="#1890ff" />
                  </view>
                </view>
              </view>
            </view>
            <view v-else class="empty-tip">
              <text>暂无附件</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 表头默认显示 -->
      <view class="list-header">
        <view class="header-title">{{ listTitle }}</view>
        <view v-if="props.mode !== 'view'">
          <ActionButton type="primary" text="补课" @click="handleMakeupCourse" />
        </view>
      </view>

      <!-- 有方案时显示列表 -->
      <view v-if="planList.length > 0" class="plan-list">
        <view class="plan-item" v-for="item in planList" :key="item.scheduleId">
          <!-- 序号和类型 -->
          <view class="item-header">
            <view class="item-index">{{ item.index }}</view>
            <view class="item-type" :class="getTypeClass(item.type)">
              {{ getTypeText(item.type) }}
            </view>
          </view>

          <!-- 原课程信息 -->
          <view class="course-info">
            <view class="info-label">原课程信息</view>
            <view class="info-content">{{ parseSourceInfo(item) }}</view>
          </view>

          <!-- 现调停补课信息 -->
          <view class="course-info">
            <view class="info-label">现调停补课信息</view>
            <view class="info-content target-info" :class="{ 'stop-info': item.type === 'stop' }">
              {{ parseTargetInfo(item) }}
            </view>
          </view>

          <!-- 操作按钮 - 查看模式下隐藏 -->
          <view v-if="props.mode !== 'view'" class="item-actions">
            <ActionButton type="warning" text="撤销" @click="handleRevoke(item)" />
          </view>
        </view>
      </view>

      <!-- 无方案时显示空状态 -->
      <view v-else class="plan-empty-state flex flex-col items-center justify-center py-10">
        <wd-icon name="note" size="40" color="#999" />
        <view class="text-gray-500 mt-3">暂无调停补课方案</view>
        <view class="text-gray-400 text-sm mt-2">请先在"授课日程"中选择需要调停的课程</view>
      </view>
    </view>

    <!-- 底部按钮组 - 固定在底部，查看模式下隐藏 -->
    <view v-if="props.mode !== 'view'" class="fixed-bottom-button">
      <view class="button-group">
        <ActionButton
          type="secondary"
          text="上一步：返回授课日程"
          @click="handlePrevStep"
          :disabled="submitLoading"
        />
        <ActionButton
          type="primary"
          text="提交申请"
          @click="handleSubmit"
          :disabled="submitLoading"
        />
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.content-card {
  box-sizing: border-box;
  padding: 24rpx;
  margin-bottom: 124rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);

  // 确保在v-show隐藏时不占用空间
  &[style*='display: none'] {
    position: absolute;
    visibility: hidden;
  }
}

.card-content {
  margin-bottom: 24rpx;
}

// 表单样式
.form-section {
  box-sizing: border-box;
  width: 100%;
  padding: 32rpx;
  border: 1px solid #f0f0f0;
  border-radius: 12rpx;
}

.form-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.form-item {
  box-sizing: border-box;
  width: 100%;

  .form-label {
    font-size: 28rpx;
    font-weight: 500;
    color: #333;
  }

  .radio-container {
    width: 100%;

    .radio-item {
      margin-right: 32rpx;
      font-size: 28rpx;
      color: #333;
    }
  }

  .reason-textarea {
    box-sizing: border-box;
    width: 100%;
  }
}

.list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 16rpx;
  margin-bottom: 24rpx;
  border-bottom: 1px solid #f0f0f0;

  .header-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }

  // ActionButton组件已有自己的样式，不需要额外样式
}
// 方案列表样式
.plan-list {
  .plan-item {
    padding: 24rpx;
    margin-bottom: 24rpx;
    background-color: #fafafa;
    border: 1px solid #f0f0f0;
    border-radius: 12rpx;

    .item-header {
      display: flex;
      align-items: center;
      margin-bottom: 16rpx;

      .item-index {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48rpx;
        height: 48rpx;
        margin-right: 16rpx;
        font-size: 28rpx;
        font-weight: 600;
        color: #1890ff;
        background-color: #e6f7ff;
        border-radius: 50%;
      }

      .item-type {
        padding: 8rpx 16rpx;
        font-size: 24rpx;
        font-weight: 500;
        border-radius: 20rpx;

        &.type-adjust {
          color: #1890ff;
          background-color: #e6f7ff;
        }

        &.type-stop {
          color: #fa8c16;
          background-color: #fff2e8;
        }

        &.type-makeup {
          color: #52c41a;
          background-color: #f6ffed;
        }
      }
    }

    .course-info {
      margin-bottom: 16rpx;

      .info-label {
        margin-bottom: 8rpx;
        font-size: 26rpx;
        color: #666;
      }

      .info-content {
        font-size: 28rpx;
        line-height: 1.4;
        color: #333;

        &.target-info {
          color: #1890ff;

          &.stop-info {
            font-weight: 500;
            color: #fa8c16;
          }
        }
      }
    }

    .item-actions {
      display: flex;
      justify-content: flex-end;
      padding-top: 16rpx;
      margin-top: 16rpx;
      border-top: 1px solid #f0f0f0;
    }
  }
}

.plan-empty-state {
  min-height: 200rpx;
}

// 固定底部按钮
.fixed-bottom-button {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  box-sizing: border-box;
  padding: 24rpx;
  background-color: #ffffff;
  border-top: 1px solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);

  .button-group {
    display: flex;
    gap: 24rpx;

    // 让ActionButton占满可用空间
    :deep(.action-button) {
      flex: 1;
      justify-content: center;
    }
  }
}

// 附件列表样式（查看模式）
.attachment-list {
  width: 100%;
  margin-top: 24rpx;
}

.attachment-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  margin-bottom: 16rpx;
  background-color: #f5f7fa;
  border-radius: 8rpx;
  transition: all 0.3s;
}

.attachment-item:hover {
  background-color: #e6f7ff;
}

.attachment-info {
  display: flex;
  align-items: center;
  max-width: 80%;
  cursor: pointer;
}

.attachment-name {
  overflow: hidden;
  font-size: 28rpx;
  color: #333;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.attachment-actions {
  display: flex;
  gap: 16rpx;
}

.attachment-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  cursor: pointer;
  background-color: #fff;
  border-radius: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.attachment-action:hover {
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  transform: translateY(-2rpx);
}

.empty-tip {
  display: flex;
  justify-content: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #999;
  background-color: #f9f9f9;
  border-radius: 8rpx;
}
</style>
