/**
 * 定位相关工具函数
 * 基于天地图JS SDK实现
 */

import { getMapApiKey } from '@/service/map'

// 天地图API Key缓存
let TIANDITU_API_KEY = ''

/**
 * 获取天地图API Key
 */
async function getTiandituApiKey(): Promise<string> {
  if (TIANDITU_API_KEY) {
    return TIANDITU_API_KEY
  }

  try {
    const response = await getMapApiKey()
    TIANDITU_API_KEY = response
    return TIANDITU_API_KEY
  } catch (error) {
    console.error('获取天地图API Key失败:', error)
    throw new Error('获取天地图API Key失败')
  }
}

/**
 * 位置信息接口
 */
export interface LocationInfo {
  /** 经度 */
  longitude: number
  /** 纬度 */
  latitude: number
  /** 详细地址 */
  address: string
  /** 省份 */
  province?: string
  /** 城市 */
  city?: string
  /** 区县 */
  district?: string
  /** 街道 */
  street?: string
  /** 获取时间 */
  timestamp: number
}

/**
 * 天地图逆地理编码响应接口
 */
interface TiandituGeocodeResponse {
  status: string
  result: {
    formatted_address: string
    addressComponent: {
      nation: string
      province: string
      city: string
      county: string
      road: string
      poi: string
      poi_distance: string
      address: string
    }
  }
}

/**
 * 获取当前位置信息
 * @param options 定位选项
 * @returns Promise<LocationInfo>
 */
export function getCurrentLocation(options?: {
  /** 是否高精度定位 */
  enableHighAccuracy?: boolean
  /** 超时时间(ms) */
  timeout?: number
  /** 最大缓存时间(ms) */
  maximumAge?: number
}): Promise<LocationInfo> {
  return new Promise((resolve, reject) => {
    const defaultOptions = {
      enableHighAccuracy: true,
      timeout: 15000,
      maximumAge: 60000,
      ...options,
    }

    // 使用uni-app的定位API
    uni.getLocation({
      type: 'wgs84', // 使用WGS84坐标系，兼容H5环境
      ...defaultOptions,
      success: async (res) => {
        try {
          console.log('获取位置成功:', res)

          // 调用天地图逆地理编码获取详细地址
          const addressInfo = await getAddressByCoordinates(res.longitude, res.latitude)

          const locationInfo: LocationInfo = {
            longitude: res.longitude,
            latitude: res.latitude,
            address: addressInfo.formatted_address,
            province: addressInfo.addressComponent.province,
            city: addressInfo.addressComponent.city,
            district: addressInfo.addressComponent.county,
            street: addressInfo.addressComponent.road,
            timestamp: Date.now(),
          }

          resolve(locationInfo)
        } catch (error) {
          console.error('获取地址信息失败:', error)
          // 即使获取地址失败，也返回基本的经纬度信息
          resolve({
            longitude: res.longitude,
            latitude: res.latitude,
            address: `${res.latitude.toFixed(6)}, ${res.longitude.toFixed(6)}`,
            timestamp: Date.now(),
          })
        }
      },
      fail: (error) => {
        console.error('获取位置失败:', error)
        reject(new Error(`定位失败: ${error.errMsg || '未知错误'}`))
      },
    })
  })
}

/**
 * 根据坐标获取地址信息
 * @param longitude 经度
 * @param latitude 纬度
 * @returns Promise<TiandituGeocodeResponse['result']>
 */
export function getAddressByCoordinates(
  longitude: number,
  latitude: number,
): Promise<TiandituGeocodeResponse['result']> {
  return new Promise((resolve, reject) => {
    // 获取API Key后调用天地图API
    getTiandituApiKey()
      .then((apiKey) => {
        // 使用天地图API格式，参考老写法
        const url = `https://api.tianditu.gov.cn/geocoder?postStr={'lon':${longitude},'lat':${latitude},'ver':1}&type=geocode&tk=${apiKey}`

        // 使用fetch API而不是uni.request，因为在H5环境下更稳定
        return fetch(url)
      })
      .then((response) => response.json())
      .then((data: TiandituGeocodeResponse) => {
        console.log('天地图逆地理编码响应:', data)
        if (data && data.result) {
          resolve(data.result)
        } else {
          reject(new Error('未找到地址信息'))
        }
      })
      .catch((error) => {
        console.error('获取地址信息时出错:', error)
        reject(new Error('获取地址信息失败，请稍后再试！'))
      })
  })
}

/**
 * 格式化位置信息为显示文本
 * @param location 位置信息
 * @returns 格式化后的地址文本
 */
export function formatLocationText(location: LocationInfo): string {
  if (location.address) {
    return location.address
  }

  // 如果没有详细地址，使用省市区拼接
  const parts = [location.province, location.city, location.district, location.street].filter(
    Boolean,
  )

  if (parts.length > 0) {
    return parts.join('')
  }

  // 最后使用经纬度
  return `${location.latitude.toFixed(6)}, ${location.longitude.toFixed(6)}`
}

/**
 * 检查是否支持定位功能
 * @returns boolean
 */
export function isLocationSupported(): boolean {
  return typeof uni.getLocation === 'function'
}

/**
 * 计算两点之间的距离（米）
 * @param lat1 第一个点的纬度
 * @param lon1 第一个点的经度
 * @param lat2 第二个点的纬度
 * @param lon2 第二个点的经度
 * @returns 距离（米）
 */
export function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 6371000 // 地球半径（米）
  const dLat = ((lat2 - lat1) * Math.PI) / 180
  const dLon = ((lon2 - lon1) * Math.PI) / 180
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos((lat1 * Math.PI) / 180) *
      Math.cos((lat2 * Math.PI) / 180) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  return R * c
}
