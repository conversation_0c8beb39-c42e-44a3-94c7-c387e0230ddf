import request from '@/utils/request'
import type {
  LeaveResponse,
  LeaveRecordResponse,
  LeaveQueryParams,
  LeaveApplicationForm,
} from '@/types/leave'

/**
 * 获取学生请假记录（旧版）
 * @deprecated 请使用getStudentLeaveRecordsNew
 * @param params 查询参数
 * @returns 请假记录响应数据
 */
export function getStudentLeaveRecords(params: LeaveQueryParams): Promise<LeaveResponse> {
  return request('/student_server/student_leave', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取学生请假记录（新版）
 * @param params 查询参数
 * @returns 请假记录响应数据
 */
export function getStudentLeaveRecordsNew(params: LeaveQueryParams): Promise<LeaveRecordResponse> {
  return request('/student_server/student_leave', {
    method: 'POST',
    data: params,
  })
}

/**
 * 提交请假申请（旧版API）
 * @param data 请假申请表单数据
 * @returns 提交结果
 */
export function submitLeaveApplicationOld(data: LeaveApplicationForm): Promise<any> {
  return request('/student_server/apply_leave', {
    method: 'POST',
    data,
  })
}

/**
 * 提交请假申请（新版API）
 * @param data 请假申请表单数据
 * @returns 提交结果
 */
export function submitLeaveApplication(data: LeaveApplicationForm): Promise<any> {
  return request('/student_server/student_leave/save', {
    method: 'POST',
    data,
  })
}

/**
 * 获取请假详情
 * @param id 请假记录ID
 * @returns 请假详情
 */
export function getLeaveDetail(id: number): Promise<any> {
  return request('/student_server/leave_detail', {
    method: 'POST',
    data: { id },
  })
}

/**
 * 撤销请假申请
 * @param id 请假记录ID
 * @returns 撤销结果
 */
export function cancelLeaveApplication(id: number): Promise<any> {
  return request('/student_server/cancel_leave', {
    method: 'POST',
    data: { id },
  })
}

/**
 * 提交销假申请
 * @param data 销假申请数据
 * @returns 提交结果
 */
export function submitLeaveReturn(data: {
  id: number
  wqsq: number
  qjlx: string
  sfsqcxm: number
  qjss: number
  xjsm: string
  fjlb2?: string
  xjdw?: string
}): Promise<any> {
  return request('/student_server/student_leave/cancelLeave', {
    method: 'POST',
    data,
  })
}
