<script lang="ts" setup>
// 调停补课方案组件
import { ref, watch, nextTick, onMounted, computed } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import dayjs from 'dayjs'
import { getTeacherScheduleList } from '@/service/schedule'
import { useMediationCourseStore } from '@/store/mediationCourse'
import type { TeacherScheduleItem, TeacherScheduleQuery } from '@/types/schedule'
import type { MediationCourseDetail } from '@/types/mediationCourse'
import ActionButton from '@/components/common/ActionButton.vue'
import StatusTag, { type StatusType } from '@/components/common/StatusTag.vue'

// 定义props
const props = defineProps<{
  id?: string
  mode?: string
  type?: string
  detailData?: MediationCourseDetail | null
  loading?: boolean
}>()

// 定义emit事件
const emit = defineEmits<{
  'next-step': []
}>()

// 处理下一步
const handleNextStep = () => {
  emit('next-step')
}

// 计算按钮文本
const nextStepButtonText = computed(() => {
  if (props.type === 'substitute') {
    return '下一步：制定代课方案'
  }
  return '下一步：制定调停补课方案'
})

// 定义日期选择范围
const startDate = ref<number>(Date.now()) // 开始日期，默认当天
const endDate = ref<number>(Date.now() + 7 * 24 * 60 * 60 * 1000) // 结束日期，默认一周后
const minDate = Date.now() - 90 * 24 * 60 * 60 * 1000 // 最小可选日期（前90天）
const maxDate = Date.now() + 180 * 24 * 60 * 60 * 1000 // 最大可选日期（后180天）

// 标志位：是否已经初始化过详情数据的时间
const isDetailTimeInitialized = ref(false)

// 监听详情数据变化，只在初次加载时设置调停课开始和结束时间
watch(
  () => props.detailData,
  (newDetailData) => {
    // 只在编辑模式下且还未初始化过时间时才设置
    if (newDetailData && props.mode === 'edit' && !isDetailTimeInitialized.value) {
      // 设置调停课开始时间 ttkkssj
      if (newDetailData.ttkkssj) {
        const startTime = dayjs(newDetailData.ttkkssj).valueOf()
        startDate.value = startTime
        console.log('初次设置调停课开始时间:', newDetailData.ttkkssj, startTime)
      }

      // 设置调停课结束时间 ttkjssj
      if (newDetailData.ttkjssj) {
        const endTime = dayjs(newDetailData.ttkjssj).valueOf()
        endDate.value = endTime
        console.log('初次设置调停课结束时间:', newDetailData.ttkjssj, endTime)
      }

      // 标记已初始化，防止后续覆盖用户修改
      isDetailTimeInitialized.value = true

      // 如果时间已设置，自动加载授课日程数据
      if (newDetailData.ttkkssj && newDetailData.ttkjssj) {
        console.log('编辑模式，使用详情数据时间自动加载授课日程')
        querySchedule()
      }
    }
  },
  { immediate: true },
)

// 页面初始化时自动加载数据
onMounted(() => {
  // 如果不是编辑模式，或者编辑模式但还没有详情数据，则使用默认时间加载
  if (props.mode !== 'edit' || !props.detailData) {
    console.log('页面初始化，使用默认时间自动加载授课日程')
    querySchedule()
  }
})

// popup控制变量
const startDatePopupVisible = ref(false)
const endDatePopupVisible = ref(false)

// 查询参数
const queryParams = ref<TeacherScheduleQuery>({
  spzt: 1,
  sortBy: 'skrq',
  sortOrder: 'desc',
  skrq: [],
})

// 授课日程列表数据
const scheduleList = ref<TeacherScheduleItem[]>([])

// 加载状态
// eslint-disable-next-line vue/no-dupe-keys
const loading = ref(false)

// 是否显示数据
const showData = ref(false)

// 格式化显示日期
const formatDate = (timestamp: number) => {
  return dayjs(timestamp).format('YYYY-MM-DD')
}

// 格式化授课日期显示 - 显示为：2025-07-02 第20周 星期3
const formatScheduleDate = (skrq: string, zc: number) => {
  const date = dayjs(skrq)
  const weekDay = date.day() === 0 ? 7 : date.day() // 将周日(0)转换为7，保持周一到周日为1-7
  return `${skrq} 第${zc}周 星期${weekDay}`
}

// 格式化调课后的信息显示 - 显示为：2025-04-27 第10周 星期7 1-2节
const formatAdjustedInfo = (item: TeacherScheduleItem) => {
  const adjustCourseDataMap = mediationCourseStore.getAllAdjustCourseData()
  const adjustData = Object.values(adjustCourseDataMap).find((data) => {
    return data.scheduleId === item.id && data.type === 'change'
  })

  if (!adjustData) {
    return ''
  }

  // 从store中获取调课后的信息
  const targetDate = adjustData.targetDate
  const targetSection = adjustData.targetSection
  const targetWeek = adjustData.targetWeek

  if (!targetDate || !targetSection || !targetWeek) {
    return ''
  }

  // 解析目标日期，计算周次和星期
  const date = dayjs(targetDate)
  const weekDay = date.day() === 0 ? 7 : date.day() // 将周日(0)转换为7

  return `${targetDate} 第${targetWeek}周 星期${weekDay} ${targetSection}节`
}

// 格式化课程标题显示 - 显示为：选修教学班13848 测试课程(余新华)
const formatCourseTitle = (item: TeacherScheduleItem) => {
  const className = item.skbjmc || '' // 上课班级名称
  const courseName = item.kcmc || '' // 课程名称
  const teacherName = item.zdjsxm || '' // 主讲教师姓名

  return `${className} ${courseName}(${teacherName})`
}

// 强制正常状态的课程ID集合（用于编辑模式下撤销后覆盖审核状态）
const forceNormalScheduleIds = ref<Set<number>>(new Set())

// 获取课程状态信息
const getCourseStatus = (item: TeacherScheduleItem): { text: string; type: StatusType } => {
  // 检查store中是否有该课程的调课数据
  const adjustCourseDataMap = mediationCourseStore.getAllAdjustCourseData()

  const hasAdjustData = Object.values(adjustCourseDataMap).some((data) => {
    return data.scheduleId === item.id && data.type === 'change'
  })

  const hasStopData = Object.values(adjustCourseDataMap).some((data) => {
    return data.scheduleId === item.id && data.type === 'stop'
  })

  // 在编辑模式下，如果课程与store中的数据一致，优先显示store状态
  if (props.mode === 'edit' && (hasAdjustData || hasStopData)) {
    if (hasAdjustData) {
      return { text: '已调课', type: 'primary' }
    }
    if (hasStopData) {
      return { text: '已停课', type: 'danger' }
    }
  }

  // 在编辑模式下，如果课程被标记为强制正常状态，跳过审核状态检查
  if (props.mode === 'edit' && forceNormalScheduleIds.value.has(item.id)) {
    // 检查是否已授课确认（教师确认或学生确认）
    if (item.skjhjsqrzt > 0 || item.skjhxsqrzt > 0) {
      return { text: '已授课确认', type: 'purple' }
    }
    // 直接返回默认状态：可操作
    return { text: '', type: 'default' }
  }

  // 调停补课申请状态优先级最高 - 如果有任何审核中的状态，优先显示审核状态
  if (item.ttbksqzt !== 0) {
    const statusMap: Record<number, { text: string; type: StatusType }> = {
      2: { text: '停课审核中', type: 'warning' },
      3: { text: '补课审核中', type: 'warning' },
      4: { text: '调课审核中', type: 'warning' },
      5: { text: '代课审核中', type: 'warning' },
      7: { text: '对调课审核中', type: 'warning' },
      8: { text: '自习审核中', type: 'warning' },
    }
    return statusMap[item.ttbksqzt] || { text: '审核中', type: 'warning' }
  }

  // 检查是否已授课确认（教师确认或学生确认）
  if (item.skjhjsqrzt > 0 || item.skjhxsqrzt > 0) {
    return { text: '已授课确认', type: 'purple' }
  }

  // 非编辑模式下，检查store中的调课数据
  if (hasAdjustData) {
    return { text: '已调课', type: 'primary' }
  }

  if (hasStopData) {
    return { text: '已停课', type: 'danger' }
  }

  // 默认状态：可操作
  return { text: '', type: 'default' }
}

// 日期选择变化处理函数
const handleStartDateChange = ({ value }: { value: number }) => {
  startDate.value = value
  // 确保结束日期不小于开始日期
  if (endDate.value < startDate.value) {
    endDate.value = startDate.value
  }
  // 更新store中的时间范围
  mediationCourseStore.setTimeRange(startDate.value, endDate.value)
  // 选择后关闭popup
  startDatePopupVisible.value = false
  // 日期变化后自动重新查询数据
  querySchedule()
}

const handleEndDateChange = ({ value }: { value: number }) => {
  endDate.value = value
  // 更新store中的时间范围
  mediationCourseStore.setTimeRange(startDate.value, endDate.value)
  // 选择后关闭popup
  endDatePopupVisible.value = false
  // 日期变化后自动重新查询数据
  querySchedule()
}

// 获取调课store
const mediationCourseStore = useMediationCourseStore()

// 操作按钮处理
const handleAdjustCourse = (item: TeacherScheduleItem) => {
  console.log('调课', item)

  // 构建课程信息对象，存储到store中
  const courseData = {
    courseName: formatCourseTitle(item), // 使用格式化后的课程标题
    scheduleDate: formatScheduleDate(item.skrq, item.zc), // 使用格式化后的授课日期
    sections: item.jcshow || item.jc, // 节次信息
    venue: item.skcdmc || '无需', // 场地信息
    // 调课计划查询所需的参数
    scheduleParams: {
      changeType: 4, // 调课类型
      scheduleId: item.id, // 排课ID
      teachTaskId: item.jxrwid, // 教学任务ID
    },
    // 保存原始数据以备后用
    originalData: item,
  }

  // 将课程数据存储到store中
  mediationCourseStore.setCurrentCourseInfo(courseData)

  // 跳转到调课页面
  uni.navigateTo({
    url: `/pages/teacher/mediation-course/adjust-course/index?courseId=${item.id}&jxrwid=${item.jxrwid}`,
  })
}

const handleSuspendCourse = (item: TeacherScheduleItem) => {
  console.log('停课', item)

  // 模仿PC端停课逻辑，构造停课数据
  const sourceRemark = `${item.kcmc}(${item.skbjmc}) ${item.skrq} 第${item.zc}周 星期${item.xqs} ${item.jcshow}节 ${item.skcdmc ?? ''}`

  // 构造PC端格式的停课数据
  const stopCourseData = {
    type: 'stop' as const, // 停课类型
    scheduleId: item.id,
    key: `[停课]第${item.zc}周 ${item.skrq} 星期${item.xqs} ${item.jcshow} ${item.skcdmc}>>>${item.id}`,
    teachingTaskId: item.jxrwid,
    sourceRemark,
    targetRemark: '', // 停课没有目标备注
    ks: item.ks || 2, // 课时，从item中获取或默认2
    targetDate: '', // 停课没有目标日期
    targetWeek: 0, // 停课没有目标日期
    targetSection: '', // 停课没有目标节次
    targetVenueCode: '', // 停课没有目标场地
    teacherCode: '', // 停课没有目标教师
    teachingClassCode: '', // 停课没有目标班级
    mergeTeachingTaskId: null, // 停课没有合并任务ID
    _X_ROW_KEY: `row_${Date.now()}`,
  }

  // 存储到store中
  const scheduleIdKey = String(stopCourseData.scheduleId)
  mediationCourseStore.addAdjustCourseData(scheduleIdKey, stopCourseData)

  // 在编辑模式下，从强制正常状态集合中移除该课程ID
  if (props.mode === 'edit') {
    forceNormalScheduleIds.value.delete(item.id)
    console.log('已从强制正常状态集合中移除课程ID:', item.id)
  }

  uni.showToast({
    title: '停课方案已保存',
    icon: 'success',
  })

  console.log('停课数据已保存:', stopCourseData)
}

// 撤销调课或停课操作
const handleCancelAdjustment = (item: TeacherScheduleItem) => {
  console.log('撤销操作', item)

  // 从store中删除对应的调课数据
  const scheduleIdKey = String(item.id)
  mediationCourseStore.removeAdjustCourseData(scheduleIdKey)

  // 在编辑模式下，将课程ID添加到强制正常状态集合中，覆盖审核状态
  if (props.mode === 'edit') {
    forceNormalScheduleIds.value.add(item.id)
    console.log('已将课程ID添加到强制正常状态集合:', item.id)
  }

  uni.showToast({
    title: '撤销成功',
    icon: 'success',
  })

  console.log('已撤销课程调整:', scheduleIdKey)
}

// 打开日期选择器popup
const openStartDatePicker = () => {
  startDatePopupVisible.value = true
}

const openEndDatePicker = () => {
  endDatePopupVisible.value = true
}

// 关闭popup
const closeStartDatePopup = () => {
  startDatePopupVisible.value = false
}

const closeEndDatePopup = () => {
  endDatePopupVisible.value = false
}

// 监听日期变化
watch([startDate, endDate], () => {
  // 可以在这里添加日期变化时的处理逻辑
})

// 查询授课日程
const querySchedule = async () => {
  try {
    loading.value = true
    showData.value = false

    // 准备查询参数
    queryParams.value.skrq = [formatDate(startDate.value), formatDate(endDate.value)]

    // 调用API获取数据
    const response = await getTeacherScheduleList(queryParams.value)

    if (response && response.items) {
      scheduleList.value = response.items
      showData.value = scheduleList.value.length > 0
    } else {
      scheduleList.value = []
    }
  } catch (error) {
    console.error('获取授课日程失败', error)
    scheduleList.value = []
  } finally {
    loading.value = false
  }
}

// 页面显示时的处理 - 检查store中的调课数据并更新状态
onShow(() => {
  console.log('页面显示，检查调课数据状态')

  // 获取store中的调课数据
  const adjustCourseDataMap = mediationCourseStore.getAllAdjustCourseData()

  if (Object.keys(adjustCourseDataMap).length > 0) {
    console.log('发现调课数据，更新页面状态:', adjustCourseDataMap)

    // 如果有数据且当前显示了课程列表，则强制重新渲染以更新状态
    if (showData.value && scheduleList.value.length > 0) {
      // 触发响应式更新，让getCourseStatus重新计算
      const currentList = [...scheduleList.value]
      scheduleList.value = []
      nextTick(() => {
        scheduleList.value = currentList
      })
    }
  }
})
</script>

<template>
  <view class="content-card">
    <!-- 添加调停课时间选择 -->
    <view class="form-section">
      <view class="section-title">调停课时间</view>

      <!-- 调课时间区域 -->
      <view class="time-section">
        <view class="time-section-label">调课时间</view>
        <!-- 调课日期 - 开始时间和结束时间在一行 -->
        <view class="form-item">
          <view class="time-row">
            <!-- 开始时间 -->
            <view class="time-item">
              <view class="form-label required">开始时间</view>
              <view class="form-content">
                <view class="picker-display" @click="openStartDatePicker">
                  <text>{{ formatDate(startDate) }}</text>
                  <wd-icon name="arrow-right" size="16" class="text-gray-300" />
                </view>
              </view>
            </view>

            <!-- 结束时间 -->
            <view class="time-item">
              <view class="form-label required">结束时间</view>
              <view class="form-content">
                <view class="picker-display" @click="openEndDatePicker">
                  <text>{{ formatDate(endDate) }}</text>
                  <wd-icon name="arrow-right" size="16" class="text-gray-300" />
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 开始日期选择popup -->
      <wd-popup
        v-model="startDatePopupVisible"
        position="bottom"
        close-on-click-modal
        :z-index="1000"
      >
        <view class="popup-header">
          <view class="text-gray-500" @click="closeStartDatePopup">取消</view>
          <view class="popup-title">选择开始日期</view>
          <view class="text-blue-500" @click="closeStartDatePopup">确定</view>
        </view>
        <view class="calendar-container">
          <wd-calendar-view
            v-model="startDate"
            @change="handleStartDateChange"
            :min-date="minDate"
            :max-date="maxDate"
            :panel-height="400"
            :first-day-of-week="1"
            show-current
          />
        </view>
      </wd-popup>

      <!-- 结束日期选择popup -->
      <wd-popup
        v-model="endDatePopupVisible"
        position="bottom"
        close-on-click-modal
        :z-index="1000"
      >
        <view class="popup-header">
          <view class="text-gray-500" @click="closeEndDatePopup">取消</view>
          <view class="popup-title">选择结束日期</view>
          <view class="text-blue-500" @click="closeEndDatePopup">确定</view>
        </view>
        <view class="calendar-container">
          <wd-calendar-view
            v-model="endDate"
            @change="handleEndDateChange"
            :min-date="startDate"
            :max-date="maxDate"
            :panel-height="400"
            :first-day-of-week="1"
            show-current
          />
        </view>
      </wd-popup>

      <!-- 查询按钮 -->
      <view class="button-group-flex">
        <ActionButton class="w-full" type="primary" text="查询授课日程" @click="querySchedule" />
      </view>
    </view>

    <!-- 授课日程列表 -->
    <view class="form-section">
      <view class="section-title">授课日程列表</view>

      <!-- 数据列表 -->
      <view v-if="showData && scheduleList.length > 0" class="schedule-list">
        <!-- 授课日程卡片 -->
        <view v-for="item in scheduleList" :key="item.id" class="schedule-card">
          <view class="card-header">
            <view class="card-title">{{ formatCourseTitle(item) }}</view>
            <!-- 动态状态标识 -->
            <StatusTag :text="getCourseStatus(item).text" :type="getCourseStatus(item).type" />
          </view>
          <view class="card-content">
            <view class="info-item">
              <view class="info-label">
                <wd-icon name="calendar" size="16" color="#1890ff" />
                <text>授课日期</text>
              </view>
              <view class="info-value">{{ formatScheduleDate(item.skrq, item.zc) }}</view>
            </view>
            <view class="info-item">
              <view class="info-label">
                <wd-icon name="time" size="16" color="#1890ff" />
                <text>节次</text>
              </view>
              <view class="info-value">{{ item.jcshow || item.jc }}</view>
            </view>
            <view class="info-item">
              <view class="info-label">
                <wd-icon name="location" size="16" color="#1890ff" />
                <text>教学场地</text>
              </view>
              <view class="info-value">{{ item.skcdmc || '无需' }}</view>
            </view>
          </view>
          <view class="card-footer">
            <!-- 左侧：显示调课后信息（仅已调课状态显示） -->
            <view class="footer-left">
              <view
                v-if="getCourseStatus(item).type === 'primary' && formatAdjustedInfo(item)"
                class="adjusted-info"
              >
                <text class="adjusted-label">现调至：</text>
                <text class="adjusted-value">{{ formatAdjustedInfo(item) }}</text>
              </view>
            </view>

            <!-- 右侧：根据课程状态显示不同内容 -->
            <view class="footer-right">
              <template v-if="getCourseStatus(item).type === 'default'">
                <!-- 正常状态：显示操作按钮 -->
                <ActionButton type="primary" @click="handleAdjustCourse(item)">
                  <wd-icon name="refresh" size="16" />
                  <text class="ml-1">调课</text>
                </ActionButton>
                <ActionButton type="danger" @click="handleSuspendCourse(item)">
                  <wd-icon name="pause" size="16" />
                  <text class="ml-1">停课</text>
                </ActionButton>
              </template>
              <template
                v-else-if="
                  getCourseStatus(item).type === 'primary' ||
                  getCourseStatus(item).type === 'danger'
                "
              >
                <!-- 已调课或已停课状态：显示撤销按钮 -->
                <ActionButton type="warning" @click="handleCancelAdjustment(item)">
                  <wd-icon name="close" size="16" />
                  <text class="ml-1">撤销</text>
                </ActionButton>
              </template>
              <template v-else>
                <!-- 其他状态：显示状态信息 -->
                <view
                  class="status-info"
                  :class="{
                    success: getCourseStatus(item).type === 'success',
                    'text-orange-600': getCourseStatus(item).type === 'warning',
                    'text-purple-600': getCourseStatus(item).type === 'purple',
                  }"
                >
                  <text>{{ getCourseStatus(item).text }}</text>
                </view>
              </template>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载状态 -->
      <view
        v-else-if="loading"
        class="loading-state flex flex-col items-center justify-center py-10"
      >
        <wd-loading color="#1890ff" size="36" />
        <view class="text-gray-500 mt-3">加载中...</view>
      </view>

      <!-- 空状态 -->
      <view v-else class="plan-empty-state flex flex-col items-center justify-center py-10">
        <wd-icon name="note" size="40" color="#999" />
        <view class="text-gray-500 mt-3">暂无调停补课方案</view>
      </view>
    </view>

    <!-- 下一步按钮 - 固定在底部，默认显示 -->
    <view class="fixed-bottom-button">
      <ActionButton type="primary" :text="nextStepButtonText" @click="handleNextStep" />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.content-card {
  box-sizing: border-box;
  padding-bottom: 120rpx; // 为固定按钮留出空间
  margin-bottom: 24rpx;
  background-color: transparent;

  // 确保在v-show隐藏时不占用空间
  &[style*='display: none'] {
    position: absolute;
    visibility: hidden;
  }
}

.form-section {
  box-sizing: border-box;
  padding: 24rpx;
  margin-bottom: 24rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  padding-bottom: 16rpx;
  margin-bottom: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  border-bottom: 1px solid #f0f0f0;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
  background-color: #fff;
  border-bottom: 1px solid #ebedf0;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.calendar-container {
  height: auto;
  max-height: 80vh;
  padding: 20rpx;
  background-color: #fff;
}
/* 调课时间区域样式 */
.time-section {
  padding: 20rpx;
  margin-bottom: 30rpx;
  border: 1px solid #e8e8e8;
  border-radius: 8rpx;
}

.time-section-label {
  padding-bottom: 10rpx;
  margin-bottom: 16rpx;
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
  border-bottom: 1px dashed #e8e8e8;
}

.form-item {
  box-sizing: border-box;
  width: 100%;
  margin-bottom: 20rpx;
}
/* 时间选择器一行布局样式 */
.time-row {
  display: flex;
  gap: 20rpx;
  align-items: flex-start;
}

.time-item {
  flex: 1;

  .form-label {
    margin-bottom: 12rpx;
    font-size: 28rpx;
    color: #333333;

    &.required::before {
      margin-right: 4rpx;
      color: #f5222d;
      content: '*';
    }
  }

  .form-content {
    position: relative;
  }

  .picker-display {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80rpx;
    padding: 0 20rpx;
    font-size: 28rpx;
    color: #333333;
    background-color: #ffffff;
    border: 1px solid #e8e8e8;
    border-radius: 8rpx;

    .placeholder {
      color: #999999;
    }
  }
}

.plan-empty-state {
  min-height: 200rpx;
}

.button-group-flex {
  display: flex;
  justify-content: center;
  width: 100%;
  margin-top: 32rpx;
}
/* 授课日程卡片样式 */
.schedule-list {
  box-sizing: border-box;
  width: 100%;
}

.schedule-card {
  box-sizing: border-box;
  width: 100%;
  margin-bottom: 20rpx;
  overflow: hidden;
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.card-header {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #f9f9f9;
  border-bottom: 1px solid #f0f0f0;
}

.card-title {
  flex: 1;
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
}

.card-content {
  box-sizing: border-box;
  padding: 20rpx;
}

.info-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.info-label {
  display: flex;
  align-items: center;
  color: #666666;

  text {
    margin-left: 8rpx;
    font-size: 26rpx;
  }
}

.info-value {
  font-size: 26rpx;
  color: #333333;
}

.card-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 20rpx;
  border-top: 1px solid #f0f0f0;
}

.footer-left {
  display: flex;
  flex: 1;
  align-items: center;
}

.footer-right {
  display: flex;
  gap: 12rpx;
  align-items: center;
}

.adjusted-info {
  display: flex;
  align-items: center;
  font-size: 24rpx;

  .adjusted-label {
    margin-right: 8rpx;
    color: #666666;
  }

  .adjusted-value {
    font-weight: 500;
    color: #1890ff;
  }
}

// ActionButton组件样式已在组件内部定义，此处不再需要.action-button样式

.status-info {
  display: flex;
  align-items: center;
  font-size: 26rpx;

  text {
    margin-left: 4rpx;
  }

  &.success {
    color: #52c41a;
  }

  &.warning {
    color: #fa8c16;
  }

  // 紫色状态
  &.purple {
    color: #722ed1;
  }
}

// 固定底部按钮
.fixed-bottom-button {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  box-sizing: border-box;
  padding: 24rpx;
  background-color: #ffffff;
  border-top: 1px solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
}
</style>
